package com.travelsky.ppp.improved.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.travelsky.ppp.framework.common.Result;
import com.travelsky.ppp.framework.common.Views;
import com.travelsky.ppp.improved.controller.dto.IssueListDto;
import com.travelsky.ppp.improved.controller.dto.IssueListQueryDto;
import com.travelsky.ppp.improved.domain.service.IssueListService;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * ClassName:IssueListController
 * Package:com.travelsky.ppp.improved.controller
 * Description:
 *
 * @date:2025/6/13 9:49
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@RestController
@RequestMapping("/issueList")
public class IssueListController {

    private final IssueListService issueListService;

    public IssueListController(IssueListService issueListService) {
        this.issueListService = issueListService;
    }

    /**
     * 自建问题列表
     * @return 创建结果
     */
    @PostMapping("/createIssueList")
    public Result<Void> createIssueList(@JsonView(Views.RequestAdd.class)
                                            @RequestBody
                                            @Valid
                                            IssueListDto issueListDto) {
        issueListService.createIssueList(issueListDto);
        return Result.success();
    }

    /**
     * 问题确认
     * @return 创建结果
     */
    @PostMapping("/ensureIssueList")
    @Valid
    public Result<Void> ensureIssueList(@JsonView(Views.RequestUpdate.class)
                                            @RequestBody
                                            @Valid
                                            IssueListDto issueListDto) {
        issueListService.ensureIssueList(issueListDto);
        return Result.success();
    }

    /**
     * 删除问题清单
     * <AUTHOR>
     * @date 2025/6/13 15:17
     * @param id
     * @return com.travelsky.ppp.framework.common.Result<java.lang.Void>
     */
    @GetMapping("/delete/{id}")
    public Result<Void> deleteIssueList(@PathVariable Long id) {
        issueListService.deleteIssueList(id);
        return Result.success();
    }

    /**
     * 查询问题清单
     * <AUTHOR>
     * @date 2025/6/13 15:17
     * @param queryDto
     * @return com.travelsky.ppp.framework.common.Result<java.util.List<com.travelsky.ppp.improved.controller.dto.IssueListDto>>
     */
    @PostMapping("/query")
    public Result<List<IssueListDto>> query(@RequestBody IssueListQueryDto queryDto) {
        return Result.success(issueListService.query(queryDto));
    }
}
