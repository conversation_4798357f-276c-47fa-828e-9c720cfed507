package com.travelsky.ppp.improved.controller.dto;

import com.facebook.presto.jdbc.internal.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.time.LocalDate;

/**
 * ClassName:IssueListQueryDto
 * Package:com.travelsky.ppp.improved.controller.dto
 * Description:
 * 问题清单查询条件
 * @date:2025/6/13 14:45
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
public class IssueListQueryDto {

    private String softwareVersion;
    private String description;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM")
    private LocalDate queryMonth = LocalDate.now();
    @Pattern(regexp = "^(TODO|IN_PROGRESS|READY|DONE)$",message = "问题状态输入异常")
    private String status = "TODO";


}
