package com.travelsky.ppp.improved.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.travelsky.ppp.improved.domain.constants.ImprovementsStatus;
import com.travelsky.ppp.improved.domain.repository.po.ActionItemPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName:ImprovementPo
 * Package:com.travelsky.ppp.improved.infrastructure.model
 * Description:
 *
 * @date:2025/6/16 10:28
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Improvement {

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 改进对象（默认软件版本） */
    private String improveObject;

    /** 指标名称 */
    private String metricName;

    /** 当前值 */
    private Double currentValue;

    /** 目标值 */
    private Double targetValue;

    /** 行动项：JSON 数组，元素为 Markdown 文本 */
    private List<ActionItemPo> actionItems;

    /** 行动负责人 */
    private String actionOwner;

    /** 预计达成时间 */
    private LocalDateTime expectedCompletionTime;

    /** 改进启动时间 */
    private LocalDateTime startTime;

    /** 结果分析 */
    private String resultAnalysis;

    /** 改进状态：NOT_STARTED / IN_PROGRESS / COMPLETED */
    private String status;

    /** 实际月 */
    private LocalDateTime actualMonth;

    /** 实际值 */
    private Double actualValue;

    /** 实际达成时间 */
    private LocalDateTime actualCompletionTime;

    /** 是否达标 */
    private boolean compliant;

    /**
     * 从问题清单创建改进项
     * <AUTHOR>
     * @date 2025/6/16 10:50
     * @param improveObject
     * @param targetValue
     * @param actionOwner
     * @param expectedCompletionTime
     * @return com.travelsky.ppp.improved.domain.entity.Improvement
     */
    public Improvement create(String improveObject,
                              String actionOwner,
                              LocalDateTime expectedCompletionTime) {
        this.improveObject = improveObject;
        this.actionOwner = actionOwner;
        this.expectedCompletionTime = expectedCompletionTime;
        this.status = ImprovementsStatus.NOT_STARTED;
        return this;
    }
}
