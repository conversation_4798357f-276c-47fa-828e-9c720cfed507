package com.travelsky.ppp.improved.domain.entity;

import com.travelsky.ppp.improved.domain.repository.po.IssueListPo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * ClassName:IssueListFactory
 * Package:com.travelsky.ppp.improved.domain.entity
 * Description:
 *
 * @date:2025/6/13 13:51
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Service
public class IssueListFactory {

    public IssueListPo createIssueListPo(IssueList issueList) {
        final IssueListPo issueListPo = new IssueListPo();
        BeanUtils.copyProperties(issueList, issueListPo);
        return issueListPo;
    }
}
