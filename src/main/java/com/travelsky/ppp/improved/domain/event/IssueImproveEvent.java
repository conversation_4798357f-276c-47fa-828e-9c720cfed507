package com.travelsky.ppp.improved.domain.event;

import com.travelsky.ppp.improved.domain.entity.IssueList;
import com.travelsky.ppp.improved.infrastructure.event.DomainEvent;

import java.time.LocalDateTime;

/**
 * ClassName:IssueImproveEvent
 * Package:com.travelsky.ppp.improved.domain.event
 * Description:
 *
 * @date:2025/6/13 14:25
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
public class IssueImproveEvent extends DomainEvent<IssueList> {

    public static IssueImproveEvent create(IssueList issueList) {
        final IssueImproveEvent issueImproveEvent = new IssueImproveEvent();
        issueImproveEvent.setMessage(issueList);
        issueImproveEvent.setSource("IssueListService");
        issueImproveEvent.setTime(LocalDateTime.now());
        return issueImproveEvent;
    }
}
