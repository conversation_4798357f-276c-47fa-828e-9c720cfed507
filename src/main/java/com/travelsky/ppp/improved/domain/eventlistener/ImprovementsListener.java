package com.travelsky.ppp.improved.domain.eventlistener;

import com.travelsky.ppp.improved.domain.entity.Improvement;
import com.travelsky.ppp.improved.domain.entity.IssueList;
import com.travelsky.ppp.improved.domain.event.IssueImproveEvent;
import com.travelsky.ppp.improved.domain.repository.ImprovementsRepository;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * ClassName:ImprovementsListener
 * Package:com.travelsky.ppp.improved.domain.eventlistener
 * Description:
 * 问题清单提交改进事件监听
 *
 * @date:2025/6/16 10:41
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Component
public class ImprovementsListener {

    private final ImprovementsRepository improvementsRepository;

    public ImprovementsListener(ImprovementsRepository improvementsRepository) {
        this.improvementsRepository = improvementsRepository;
    }

    @EventListener(classes = IssueImproveEvent.class)
    public void onIssueImproveEvent(IssueImproveEvent event) {
        // 处理改进事件
        final IssueList message = event.getMessage();
        final Improvement improvement = Improvement.builder().build();
        improvement.create(message.getSoftwareVersion(),  message.getOwner(), message.getPlanResolveTime());
        improvementsRepository.saveImprovement();
    }
}
