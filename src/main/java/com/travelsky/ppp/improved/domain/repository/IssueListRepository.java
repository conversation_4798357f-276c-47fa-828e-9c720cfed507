package com.travelsky.ppp.improved.domain.repository;

import com.travelsky.ppp.improved.domain.repository.condition.IssueListQueryCondition;
import com.travelsky.ppp.improved.domain.repository.po.IssueListPo;

import java.util.List;

/**
 * ClassName:IssueListRepository
 * Package:com.travelsky.ppp.improved.domain.repository
 * Description:
 *
 * @date:2025/6/13 10:07
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
public interface IssueListRepository {

    void saveIssueList(IssueListPo issueList);

    void updateIssueList(IssueListPo issueList);

    void deleteIssueList(Long id);

    IssueListPo queryById(Long id);

    List<IssueListPo> queryByCondition(IssueListQueryCondition condition);
}
