package com.travelsky.ppp.improved.domain.repository.condition;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

/**
 * ClassName:ImprovementsQueryCondition
 * Package:com.travelsky.ppp.improved.domain.repository.condition
 * Description:
 *
 * @date:2025/6/16 11:16
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
@Builder
public class ImprovementsQueryCondition {
    // 改进对象 软件名 模糊匹配
    private String improveObject;
    // 指标名称 模糊匹配
    private String metricName;
    // 查询月份
    private LocalDate queryMonth;
    // 状态
    private String status;
    // 达标情况
    private boolean compliant;

}
