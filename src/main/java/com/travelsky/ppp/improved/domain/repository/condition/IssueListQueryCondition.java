package com.travelsky.ppp.improved.domain.repository.condition;

import lombok.Data;

import java.time.LocalDate;

/**
 * ClassName:IssueListQueryCondition
 * Package:com.travelsky.ppp.improved.infrastructure.model
 * Description:
 *
 * @date:2025/6/13 10:21
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
public class IssueListQueryCondition {

    /**
     * 软件版本
     */
    private String softwareVersion;

    /**
     * 问题描述 (Markdown)
     */
    private String description;

    /**
     * 状态
     */
    private String status;

    /**
     * 问题月份
     */
    private LocalDate queryMonth;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        IssueListQueryCondition condition = new IssueListQueryCondition();

        public Builder softwareVersion(String softwareVersion) {
            this.condition.softwareVersion = softwareVersion;
            return this;
        }

        public Builder description(String description) {
            this.condition.description = description;
            return this;
        }

        public Builder status(String status) {
            this.condition.status = status;
            return this;
        }

        public Builder queryMonth(LocalDate queryMoth) {
            this.condition.queryMonth = queryMoth;
            return this;
        }

        public IssueListQueryCondition build() {
            return this.condition;
        }
    }
}
