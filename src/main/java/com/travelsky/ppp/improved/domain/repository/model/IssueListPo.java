package com.travelsky.ppp.improved.infrastructure.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pqa_issue_list")
public class IssueListPo {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 软件版本
     */
    private String softwareVersion;

    /**
     * 问题描述 (Markdown)
     */
    private String description;

    /**
     * 来源
     */
    private String source;

    /**
     * 严重程度
     */
    private String severity;

    /**
     * 优先级
     */
    private String priority;

    /**
     * 原因分析 (Markdown)
     */
    private String causeAnalysis;

    /**
     * 解决措施
     */
    private String solution;

    /**
     * 责任人
     */
    private String owner;

    /**
     * 问题提出时间
     */
    private LocalDateTime issueTime = LocalDateTime.now();

    /**
     * 计划解决时间
     */
    private LocalDateTime planResolveTime;

    /**
     * 实际解决时间
     */
    private LocalDateTime actualResolveTime;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 跟踪记录（如改进链接）
     */
    private String trackingRecord;

    /**
     * 是否是自建问题，自建不允许删除
     */
    private boolean selfBuilt;




}
