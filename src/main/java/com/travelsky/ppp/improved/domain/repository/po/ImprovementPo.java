package com.travelsky.ppp.improved.infrastructure.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName:ImprovementPo
 * Package:com.travelsky.ppp.improved.infrastructure.model
 * Description:
 *
 * @date:2025/6/16 10:28
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pqa_improvement")
public class ImprovementPo {

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 改进对象（默认软件版本） */
    private String improveObject;

    /** 指标名称 */
    private String metricName;

    /** 当前值 */
    private Double currentValue;

    /** 目标值 */
    private Double targetValue;

    /** 行动项：JSON 数组，元素为 Markdown 文本 */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ActionItemPo> actionItems;

    /** 行动负责人 */
    private String actionOwner;

    /** 预计达成时间 */
    private LocalDateTime expectedCompletionTime;

    /** 改进启动时间 */
    private LocalDateTime startTime;

    /** 结果分析 */
    private String resultAnalysis;

    /** 改进状态：NOT_STARTED / IN_PROGRESS / COMPLETED */
    private String status;

    /** 实际月 */
    private LocalDateTime actualMonth;

    /** 实际值 */
    private Double actualValue;

    /** 实际达成时间 */
    private LocalDateTime actualCompletionTime;

}
