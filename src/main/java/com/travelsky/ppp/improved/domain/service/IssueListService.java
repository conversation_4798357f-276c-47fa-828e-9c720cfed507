package com.travelsky.ppp.improved.domain.service;

import com.travelsky.ppp.framework.exception.BusinessException;
import com.travelsky.ppp.framework.utils.SpringBeanTools;
import com.travelsky.ppp.improved.controller.assembler.IssueListAssembler;
import com.travelsky.ppp.improved.controller.dto.IssueListDto;
import com.travelsky.ppp.improved.controller.dto.IssueListQueryDto;
import com.travelsky.ppp.improved.domain.entity.IssueList;
import com.travelsky.ppp.improved.domain.entity.IssueListFactory;
import com.travelsky.ppp.improved.domain.event.IssueImproveEvent;
import com.travelsky.ppp.improved.domain.repository.IssueListRepository;
import com.travelsky.ppp.improved.domain.repository.condition.IssueListQueryCondition;
import com.travelsky.ppp.improved.domain.repository.po.IssueListPo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * ClassName:IssueListDomainService
 * Package:com.travelsky.ppp.improved.domain.service
 * Description:
 *
 * @date:2025/6/13 13:18
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Service
@RequiredArgsConstructor
public class IssueListService {

    private final IssueListRepository issueListRepository;

    private final IssueListFactory issueListFactory;

    /**
     * 自建问题清单
     * <AUTHOR>
     * @date  13:39
     * @param issueListDto
     */
    @Transactional(value = "db1TransactionManager", rollbackFor = Exception.class)
    public void createIssueList(IssueListDto issueListDto) {
        IssueList issueList = IssueListAssembler.toEntity(issueListDto);
        final IssueListPo issueListPo = issueListFactory.createIssueListPo(issueList.selfBuild());
        issueListRepository.saveIssueList(issueListPo);
    }

    /**
     * 问题确认
     * <AUTHOR>
     * @date  14:20
     * @param issueListDto
     */
    @Transactional(value = "db1TransactionManager", rollbackFor = Exception.class)
    public void ensureIssueList(IssueListDto issueListDto) {
        IssueList issueList = IssueListAssembler.toEntity(issueListDto);
        final IssueListPo issueListPo = issueListFactory.createIssueListPo(issueList);
        issueListRepository.updateIssueList(issueListPo);
        // 需要改进
        if (issueList.isNeedForImprovement()) {
            final IssueImproveEvent issueImproveEvent = IssueImproveEvent.create(issueList);
            // 发布改进事件
            SpringBeanTools.publishEvent(issueImproveEvent);
        }
    }

    /**
     * 删除问题
     * <AUTHOR>
     * @date 2025/6/13 14:38
     * @param id
     */
    @Transactional(value = "db1TransactionManager", rollbackFor = Exception.class)
    public void deleteIssueList(Long id) {
        final IssueListPo issueListPo = issueListRepository.queryById(id);
        Objects.requireNonNull(issueListPo, "问题清单不存在");
        if (!issueListPo.isSelfBuilt()) {
            throw new BusinessException("自动创建的问题清单不允许删除");
        }
        issueListRepository.deleteIssueList(id);
    }

    /**
     * 查询问题清单
     * <AUTHOR>
     * @date 2025/6/13 14:55
     * @param queryDto
     * @return java.util.List<com.travelsky.ppp.improved.controller.dto.IssueListDto>
     */
    public List<IssueListDto> query(IssueListQueryDto queryDto) {
        // 构建查询条件
        final IssueListQueryCondition condition = IssueListQueryCondition.builder()
                .softwareVersion(queryDto.getSoftwareVersion())
                .description(queryDto.getDescription())
                .status(queryDto.getStatus())
                .queryMonth(queryDto.getQueryMonth())
                .build();
        final List<IssueListPo> issueListPos = issueListRepository.queryByCondition(condition);
        // 实体转换
        return issueListPos.stream().map(item -> {
            final IssueListDto dto = IssueListDto.builder().build();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());
    }
}
