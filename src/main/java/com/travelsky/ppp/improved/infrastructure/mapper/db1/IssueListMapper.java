package com.travelsky.ppp.improved.infrastructure.mapper.db1;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.travelsky.ppp.improved.domain.repository.po.IssueListPo;
import com.travelsky.ppp.improved.domain.repository.condition.IssueListQueryCondition;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ClassName:IssueListMapper
 * Package:com.travelsky.ppp.improved.infrastructure.mapper.db1
 * Description:
 *
 * @date:2025/6/13 10:09
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Mapper
public interface IssueListMapper extends BaseMapper<IssueListPo> {

    List<IssueListPo> queryByCondition(IssueListQueryCondition condition);
}
