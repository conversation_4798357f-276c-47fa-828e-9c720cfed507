package com.travelsky.ppp.improved.infrastructure.repository;

import com.travelsky.ppp.improved.domain.repository.IssueListRepository;
import com.travelsky.ppp.improved.infrastructure.mapper.db1.IssueListMapper;
import com.travelsky.ppp.improved.domain.repository.po.IssueListPo;
import com.travelsky.ppp.improved.domain.repository.condition.IssueListQueryCondition;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ClassName:IssueListRepositoryAdaptor
 * Package:com.travelsky.ppp.improved.infrastructure.repository
 * Description:
 *
 * @date:2025/6/13 10:07
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Repository
@RequiredArgsConstructor
public class IssueListRepositoryAdaptor implements IssueListRepository {


    private final IssueListMapper issueListMapper;

    @Override
    public void saveIssueList(IssueListPo issueList) {
        issueListMapper.insert(issueList);
    }

    @Override
    public void updateIssueList(IssueListPo issueList) {
        issueListMapper.updateById(issueList);
    }
    @Override
    public void deleteIssueList(Long id) {
        issueListMapper.deleteById(id);
    }
    public IssueListPo queryById(Long id) {
        return issueListMapper.selectById(id);
    }

    @Override
    public List<IssueListPo> queryByCondition(IssueListQueryCondition condition) {
        return issueListMapper.queryByCondition(condition);
    }
}
